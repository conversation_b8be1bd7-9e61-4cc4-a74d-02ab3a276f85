package com.cyberself.copilot

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import android.app.NotificationManager
import android.content.SharedPreferences
import android.os.PowerManager
import android.provider.Settings
import android.net.Uri

import com.facebook.react.bridge.*
import org.json.JSONObject
import java.util.*

class AlarmModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    private val TAG = "AlarmModule"
    private val context = reactContext
    
    override fun getName(): String {
        return "AlarmModule"
    }
    
    @ReactMethod
    fun scheduleAlarm(timestamp: Double, alarmData: ReadableMap, promise: Promise) {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val alarmTime = timestamp.toLong()
            
            // Convert ReadableMap to JSON string for storage
            val dataJson = convertMapToJson(alarmData).toString()
            
            // Create intent for the alarm receiver
            val intent = Intent(context, AlarmReceiver::class.java).apply {
                action = "com.cyberself.copilot.ALARM_TRIGGER"
                putExtra("ALARM_DATA", dataJson)
            }
            
            // Create unique request code based on alarm time
            val requestCode = alarmTime.hashCode()
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // Schedule exact alarm that wakes device
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // For Android 12+ we need to check for exact alarm permission
                if (alarmManager.canScheduleExactAlarms()) {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        alarmTime,
                        pendingIntent
                    )
                } else {
                    throw Exception("Exact alarm permission not granted")
                }
            } else {
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    alarmTime,
                    pendingIntent
                )
            }
            
            // Store alarm data in SharedPreferences for persistence
            saveAlarmToPrefs(requestCode, alarmTime, dataJson)
            
            Log.d(TAG, "Alarm scheduled for ${Date(alarmTime)}")
            promise.resolve("Alarm scheduled successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to schedule alarm: ${e.message}")
            promise.reject("ALARM_ERROR", "Failed to schedule alarm: ${e.message}")
        }
    }
    
    @ReactMethod
    fun cancelAlarm(timestamp: Double, promise: Promise) {
        try {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val alarmTime = timestamp.toLong()
            val requestCode = alarmTime.hashCode()
            
            val intent = Intent(context, AlarmReceiver::class.java).apply {
                action = "com.cyberself.copilot.ALARM_TRIGGER"
            }
            
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_IMMUTABLE
            )
            
            // Cancel the alarm
            alarmManager.cancel(pendingIntent)
            pendingIntent.cancel()
            
            // Remove from SharedPreferences
            removeAlarmFromPrefs(requestCode)
            
            Log.d(TAG, "Alarm canceled for time: ${Date(alarmTime)}")
            promise.resolve("Alarm canceled successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to cancel alarm: ${e.message}")
            promise.reject("ALARM_ERROR", "Failed to cancel alarm: ${e.message}")
        }
    }
    
    @ReactMethod
    fun getAllScheduledAlarms(promise: Promise) {
        try {
            val alarms = getAlarmsFromPrefs()
            val result = Arguments.createArray()
            
            for (alarm in alarms) {
                val map = Arguments.createMap()
                map.putDouble("timestamp", alarm.first.toDouble())
                map.putString("data", alarm.second)
                result.pushMap(map)
            }
            
            promise.resolve(result)
        } catch (e: Exception) {
            promise.reject("ALARM_ERROR", "Failed to get alarms: ${e.message}")
        }
    }
    
    @ReactMethod
    fun checkPendingAlarm(promise: Promise) {
        try {
            val sharedPrefs = getAlarmTempPreferences()
            val pendingAlarm = sharedPrefs.getString("PENDING_ALARM_DATA", null)
            promise.resolve(pendingAlarm)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check pending alarm: ${e.message}")
            promise.reject("ALARM_ERROR", "Failed to check pending alarm: ${e.message}")
        }
    }
    
    @ReactMethod
    fun clearPendingAlarm(promise: Promise) {
        try {
            val sharedPrefs = getAlarmTempPreferences()
            sharedPrefs.edit().remove("PENDING_ALARM_DATA").apply()
            promise.resolve(true)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear pending alarm: ${e.message}")
            promise.reject("ALARM_ERROR", "Failed to clear pending alarm: ${e.message}")
        }
    }

    @ReactMethod
    fun checkAlarmPermission(promise: Promise) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            val hasExactAlarmPermission = alarmManager.canScheduleExactAlarms()
            
            // For Android 13+, also check notification permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                val hasNotificationPermission = notificationManager.areNotificationsEnabled()
                
                val result = Arguments.createMap()
                result.putBoolean("exactAlarm", hasExactAlarmPermission)
                result.putBoolean("notification", hasNotificationPermission)
                promise.resolve(result)
            } else {
                promise.resolve(hasExactAlarmPermission)
            }
        } else {
            promise.resolve(true)
        }
    }
    
    @ReactMethod
    fun restoreAlarmsIfNeeded(promise: Promise) {
        try {
            Log.d(TAG, "Manual alarm restoration requested from JS side")
            
            val currentTime = System.currentTimeMillis()
            val alarms = getAlarmsFromPrefs()
            
            if (alarms.isEmpty()) {
                Log.d(TAG, "No alarms found to restore")
                promise.resolve(0)
                return
            }
            
            Log.d(TAG, "Found ${alarms.size} alarms, attempting to restore them")
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
            var restoredCount = 0
            
            for (alarm in alarms) {
                val (timestamp, dataJson) = alarm
                
                // Skip alarms that have already passed
                if (timestamp <= currentTime) {
                    continue
                }
                
                try {
                    // Create intent for the alarm receiver
                    val alarmIntent = Intent(context, AlarmReceiver::class.java).apply {
                        action = "com.cyberself.copilot.ALARM_TRIGGER"
                        putExtra("ALARM_DATA", dataJson)
                    }
                    
                    // Create unique request code based on alarm time
                    val requestCode = timestamp.hashCode()
                    
                    val pendingIntent = PendingIntent.getBroadcast(
                        context,
                        requestCode,
                        alarmIntent,
                        PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                    )
                    
                    // Reschedule exact alarm
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                        if (alarmManager.canScheduleExactAlarms()) {
                            alarmManager.setExactAndAllowWhileIdle(
                                AlarmManager.RTC_WAKEUP,
                                timestamp,
                                pendingIntent
                            )
                            Log.d(TAG, "Restored alarm for ${Date(timestamp)}")
                            restoredCount++
                        } else {
                            Log.w(TAG, "Cannot schedule exact alarms - permission not granted")
                        }
                    } else {
                        alarmManager.setExactAndAllowWhileIdle(
                            AlarmManager.RTC_WAKEUP,
                            timestamp,
                            pendingIntent
                        )
                        Log.d(TAG, "Restored alarm for ${Date(timestamp)}")
                        restoredCount++
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to restore alarm: ${e.message}")
                }
            }
            
            promise.resolve(restoredCount)
        } catch (e: Exception) {
            Log.e(TAG, "Error in restoreAlarmsIfNeeded: ${e.message}")
            promise.reject("ALARM_ERROR", "Failed to restore alarms: ${e.message}")
        }
    }
    
    private fun convertMapToJson(readableMap: ReadableMap): JSONObject {
        val jsonObject = JSONObject()
        val iterator = readableMap.keySetIterator()
        
        while (iterator.hasNextKey()) {
            val key = iterator.nextKey()
            when (readableMap.getType(key)) {
                ReadableType.Boolean -> jsonObject.put(key, readableMap.getBoolean(key))
                ReadableType.Number -> jsonObject.put(key, readableMap.getDouble(key))
                ReadableType.String -> jsonObject.put(key, readableMap.getString(key))
                else -> jsonObject.put(key, readableMap.getString(key))
            }
        }
        
        return jsonObject
    }
    
    private fun saveAlarmToPrefs(requestCode: Int, timestamp: Long, data: String) {
        val sharedPrefs = getAlarmPreferences()
        val editor = sharedPrefs.edit()
        editor.putString("alarm_$requestCode", "$timestamp|$data")
        editor.apply()
    }
    
    private fun removeAlarmFromPrefs(requestCode: Int) {
        val sharedPrefs = getAlarmPreferences()
        val editor = sharedPrefs.edit()
        editor.remove("alarm_$requestCode")
        editor.apply()
    }
    
    private fun getAlarmsFromPrefs(): List<Pair<Long, String>> {
        val result = mutableListOf<Pair<Long, String>>()
        val sharedPrefs = getAlarmPreferences()
        
        for (key in sharedPrefs.all.keys) {
            if (key.startsWith("alarm_")) {
                val value = sharedPrefs.getString(key, null)
                value?.let {
                    val parts = it.split("|", limit = 2)
                    if (parts.size == 2) {
                        result.add(Pair(parts[0].toLong(), parts[1]))
                    }
                }
            }
        }
        
        return result
    }

    private fun getAlarmPreferences(): SharedPreferences {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.createDeviceProtectedStorageContext()
                .getSharedPreferences("ALARM_PREFS", Context.MODE_PRIVATE)
        } else {
            context.getSharedPreferences("ALARM_PREFS", Context.MODE_PRIVATE)
        }
    }

    private fun getAlarmTempPreferences(): SharedPreferences {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.createDeviceProtectedStorageContext()
                .getSharedPreferences("ALARM_TEMP", Context.MODE_PRIVATE)
        } else {
            context.getSharedPreferences("ALARM_TEMP", Context.MODE_PRIVATE)
        }
    }

    @ReactMethod
    fun requestBatteryOptimizationExemption(promise: Promise) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val pm = context.getSystemService(Context.POWER_SERVICE) as PowerManager
            val packageName = context.packageName
            
            if (!pm.isIgnoringBatteryOptimizations(packageName)) {
                try {
                    val intent = Intent().apply {
                        action = Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
                        data = Uri.parse("package:$packageName")
                    }
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    context.startActivity(intent)
                    promise.resolve(true)
                } catch (e: Exception) {
                    promise.reject("BATTERY_OPT_ERROR", "Failed to request battery optimization exemption")
                }
            } else {
                promise.resolve(false) // Already exempted
            }
        } else {
            promise.resolve(false) // Not needed for older Android versions
        }
    }
}