package com.cyberself.copilot

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.PowerManager
import android.util.Log
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.util.*

class BootReceiver : BroadcastReceiver() {
    private val TAG = "BootReceiver"
    
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action != Intent.ACTION_BOOT_COMPLETED) return
        
        Log.d(TAG, "Boot completed, restoring alarms")
        
        // Restore alarms directly without delays or coroutines
        val alarms = getAlarmsFromPrefs(context)
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val currentTime = System.currentTimeMillis()
        
        var restoredCount = 0
        for ((timestamp, dataJson) in alarms) {
            if (timestamp <= currentTime) continue
            
            try {
                val alarmIntent = Intent(context, AlarmReceiver::class.java).apply {
                    action = "com.cyberself.copilot.ALARM_TRIGGER"
                    putExtra("ALARM_DATA", dataJson)
                }
                
                val requestCode = timestamp.hashCode()
                val pendingIntent = PendingIntent.getBroadcast(
                    context,
                    requestCode,
                    alarmIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (alarmManager.canScheduleExactAlarms()) {
                        alarmManager.setExactAndAllowWhileIdle(
                            AlarmManager.RTC_WAKEUP,
                            timestamp,
                            pendingIntent
                        )
                        restoredCount++
                    }
                } else {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        timestamp,
                        pendingIntent
                    )
                    restoredCount++
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to restore alarm: ${e.message}")
            }
        }
        
        Log.d(TAG, "Restored $restoredCount alarms")
    }
    
    private fun getAlarmsFromPrefs(context: Context): List<Pair<Long, String>> {
        val sharedPrefs = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            context.createDeviceProtectedStorageContext()
                .getSharedPreferences("ALARM_PREFS", Context.MODE_PRIVATE)
        } else {
            context.getSharedPreferences("ALARM_PREFS", Context.MODE_PRIVATE)
        }
        
        val result = mutableListOf<Pair<Long, String>>()
        for (key in sharedPrefs.all.keys) {
            if (key.startsWith("alarm_")) {
                val value = sharedPrefs.getString(key, null)
                value?.let {
                    val parts = it.split("|", limit = 2)
                    if (parts.size == 2) {
                        result.add(Pair(parts[0].toLong(), parts[1]))
                    }
                }
            }
        }
        return result
    }
}