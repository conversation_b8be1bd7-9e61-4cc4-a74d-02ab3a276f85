package com.cyberself.copilot

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat

class AlarmService : Service() {
    private val TAG = "AlarmService"
    private val CHANNEL_ID = "alarm_channel"
    private val NOTIFICATION_ID = 1
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        // Show a foreground notification to keep service alive
        val notification = createNotification(
            "Alarm Service", 
            "Managing your alarms..."
        )
        startForeground(NOTIFICATION_ID, notification)
        
        when (intent?.action) {
            "com.cyberself.copilot.START_ALARM" -> {
                val alarmData = intent.getStringExtra("ALARM_DATA") ?: return START_NOT_STICKY
                
                // Launch the alarm activity
                val activityIntent = Intent(this, MainActivity::class.java).apply {
                    addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
                    putExtra("LAUNCH_ALARM_SCREEN", true)
                    putExtra("ALARM_DATA", alarmData)
                }
                startActivity(activityIntent)

                stopSelf()
            }
            
            "com.cyberself.copilot.RESTORE_ALARMS" -> {
                Log.d(TAG, "Service received restore alarms command")
                Thread {
                    try {
                        restoreAlarms()
                    } catch (e: Exception) {
                        Log.e(TAG, "Error restoring alarms: ${e.message}", e)
                    } finally {
                        stopSelf(startId)
                    }
                }.start()
                return START_STICKY
            }
        }
        
        return START_STICKY
    }
    
    private fun restoreAlarms() {
        Log.d(TAG, "Restoring alarms from service")
        // Retrieve all saved alarms from SharedPreferences
        val alarms = getAlarmsFromPrefs()
        val currentTime = System.currentTimeMillis()
        val alarmManager = getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        Log.d(TAG, "Found ${alarms.size} alarms to restore")
        
        for (alarm in alarms) {
            val (timestamp, dataJson) = alarm
            
            // Skip alarms that have already passed
            if (timestamp <= currentTime) {
                continue
            }
            
            // Create intent for the alarm receiver
            val alarmIntent = Intent(this, AlarmReceiver::class.java).apply {
                action = "com.cyberself.copilot.ALARM_TRIGGER"
                putExtra("ALARM_DATA", dataJson)
            }
            
            // Create unique request code based on alarm time
            val requestCode = timestamp.hashCode()
            
            val pendingIntent = PendingIntent.getBroadcast(
                this,
                requestCode,
                alarmIntent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            
            // Reschedule the alarm
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (alarmManager.canScheduleExactAlarms()) {
                        alarmManager.setExactAndAllowWhileIdle(
                            AlarmManager.RTC_WAKEUP,
                            timestamp,
                            pendingIntent
                        )
                        Log.d(TAG, "Rescheduled alarm for ${java.util.Date(timestamp)}")
                    } else {
                        Log.w(TAG, "Cannot schedule exact alarms - permission not granted")
                    }
                } else {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        timestamp,
                        pendingIntent
                    )
                    Log.d(TAG, "Rescheduled alarm for ${java.util.Date(timestamp)}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Failed to reschedule alarm: ${e.message}")
            }
        }
    }
    
    private fun getAlarmsFromPrefs(): List<Pair<Long, String>> {
        val result = mutableListOf<Pair<Long, String>>()
        val sharedPrefs = getSharedPreferences("ALARM_PREFS", Context.MODE_PRIVATE)
        
        for (key in sharedPrefs.all.keys) {
            if (key.startsWith("alarm_")) {
                val value = sharedPrefs.getString(key, null)
                value?.let {
                    val parts = it.split("|", limit = 2)
                    if (parts.size == 2) {
                        result.add(Pair(parts[0].toLong(), parts[1]))
                        Log.d(TAG, "Found saved alarm for ${java.util.Date(parts[0].toLong())}")
                    }
                }
            }
        }
        
        return result
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Alarm Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Used for alarm notifications"
                enableLights(true)
                lightColor = Color.RED
                enableVibration(true)
                setSound(null, null) // We'll play custom sounds in the activity
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(title: String, message: String): Notification {
        val notificationIntent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("LAUNCH_ALARM_SCREEN", true)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(android.R.drawable.ic_lock_idle_alarm) // Using a system icon
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setOngoing(true)
            .build()
    }
}