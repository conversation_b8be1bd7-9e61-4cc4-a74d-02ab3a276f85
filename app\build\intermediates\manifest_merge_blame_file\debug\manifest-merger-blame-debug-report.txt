1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cyberself.copilot"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\PROJECTS\main-daksh\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\PROJECTS\main-daksh\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\PROJECTS\main-daksh\AndroidManifest.xml:2:3-64
11-->D:\PROJECTS\main-daksh\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->D:\PROJECTS\main-daksh\AndroidManifest.xml:3:3-63
12-->D:\PROJECTS\main-daksh\AndroidManifest.xml:3:20-61
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\PROJECTS\main-daksh\AndroidManifest.xml:4:3-77
13-->D:\PROJECTS\main-daksh\AndroidManifest.xml:4:20-74
14    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- For Android 12+ -->
14-->D:\PROJECTS\main-daksh\AndroidManifest.xml:5:3-72
14-->D:\PROJECTS\main-daksh\AndroidManifest.xml:5:20-69
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\PROJECTS\main-daksh\AndroidManifest.xml:6:3-75
15-->D:\PROJECTS\main-daksh\AndroidManifest.xml:6:20-72
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->D:\PROJECTS\main-daksh\AndroidManifest.xml:7:3-87
16-->D:\PROJECTS\main-daksh\AndroidManifest.xml:7:20-84
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\PROJECTS\main-daksh\AndroidManifest.xml:8:3-79
17-->D:\PROJECTS\main-daksh\AndroidManifest.xml:8:20-76
18    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
18-->D:\PROJECTS\main-daksh\AndroidManifest.xml:9:3-74
18-->D:\PROJECTS\main-daksh\AndroidManifest.xml:9:20-71
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\PROJECTS\main-daksh\AndroidManifest.xml:10:3-66
19-->D:\PROJECTS\main-daksh\AndroidManifest.xml:10:20-63
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->D:\PROJECTS\main-daksh\AndroidManifest.xml:11:3-77
20-->D:\PROJECTS\main-daksh\AndroidManifest.xml:11:20-75
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->D:\PROJECTS\main-daksh\AndroidManifest.xml:12:3-76
21-->D:\PROJECTS\main-daksh\AndroidManifest.xml:12:20-73
22    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
22-->D:\PROJECTS\main-daksh\AndroidManifest.xml:13:3-79
22-->D:\PROJECTS\main-daksh\AndroidManifest.xml:13:20-76
23    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
23-->D:\PROJECTS\main-daksh\AndroidManifest.xml:14:3-71
23-->D:\PROJECTS\main-daksh\AndroidManifest.xml:14:20-68
24    <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
24-->D:\PROJECTS\main-daksh\AndroidManifest.xml:15:3-73
24-->D:\PROJECTS\main-daksh\AndroidManifest.xml:15:20-70
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->D:\PROJECTS\main-daksh\AndroidManifest.xml:16:3-77
25-->D:\PROJECTS\main-daksh\AndroidManifest.xml:16:20-75
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->D:\PROJECTS\main-daksh\AndroidManifest.xml:17:3-68
26-->D:\PROJECTS\main-daksh\AndroidManifest.xml:17:20-66
27    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
27-->D:\PROJECTS\main-daksh\AndroidManifest.xml:18:3-69
27-->D:\PROJECTS\main-daksh\AndroidManifest.xml:18:20-67
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
28-->D:\PROJECTS\main-daksh\AndroidManifest.xml:19:3-78
28-->D:\PROJECTS\main-daksh\AndroidManifest.xml:19:20-76
29    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
29-->D:\PROJECTS\main-daksh\AndroidManifest.xml:20:3-71
29-->D:\PROJECTS\main-daksh\AndroidManifest.xml:20:20-69
30    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
30-->D:\PROJECTS\main-daksh\AndroidManifest.xml:21:3-93
30-->D:\PROJECTS\main-daksh\AndroidManifest.xml:21:20-90
31    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
31-->D:\PROJECTS\main-daksh\AndroidManifest.xml:22:3-95
31-->D:\PROJECTS\main-daksh\AndroidManifest.xml:22:20-92
32    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
32-->D:\PROJECTS\main-daksh\AndroidManifest.xml:23:3-73
32-->D:\PROJECTS\main-daksh\AndroidManifest.xml:23:20-70
33    <uses-permission android:name="com.xiaomi.permission.AUTOSTART" />
33-->D:\PROJECTS\main-daksh\AndroidManifest.xml:24:3-69
33-->D:\PROJECTS\main-daksh\AndroidManifest.xml:24:20-66
34    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
34-->D:\PROJECTS\main-daksh\AndroidManifest.xml:25:3-73
34-->D:\PROJECTS\main-daksh\AndroidManifest.xml:25:20-70
35
36    <queries>
36-->D:\PROJECTS\main-daksh\AndroidManifest.xml:26:3-32:13
37        <intent>
37-->D:\PROJECTS\main-daksh\AndroidManifest.xml:27:5-31:14
38            <action android:name="android.intent.action.VIEW" />
38-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:7-58
38-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:15-56
39
40            <category android:name="android.intent.category.BROWSABLE" />
40-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:7-67
40-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:17-65
41
42            <data android:scheme="https" />
42-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:7-37
42-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:13-35
43        </intent>
44    </queries>
45
46    <permission
46-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
47        android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
51
52    <application
52-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:3-108:17
53        android:name="com.cyberself.copilot.MainApplication"
53-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:16-47
54        android:allowBackup="true"
54-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:162-188
55        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
55-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
56        android:dataExtractionRules="@xml/secure_store_data_extraction_rules"
56-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:307-376
57        android:debuggable="true"
58        android:fullBackupContent="@xml/secure_store_backup_rules"
58-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:248-306
59        android:icon="@mipmap/ic_launcher"
59-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:81-115
60        android:label="@string/app_name"
60-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:48-80
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:116-161
62        android:supportsRtl="true"
62-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:221-247
63        android:theme="@style/AppTheme" >
63-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:189-220
64        <meta-data
64-->D:\PROJECTS\main-daksh\AndroidManifest.xml:34:5-83
65            android:name="expo.modules.updates.ENABLED"
65-->D:\PROJECTS\main-daksh\AndroidManifest.xml:34:16-59
66            android:value="false" />
66-->D:\PROJECTS\main-daksh\AndroidManifest.xml:34:60-81
67        <meta-data
67-->D:\PROJECTS\main-daksh\AndroidManifest.xml:35:5-105
68            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
68-->D:\PROJECTS\main-daksh\AndroidManifest.xml:35:16-80
69            android:value="ALWAYS" />
69-->D:\PROJECTS\main-daksh\AndroidManifest.xml:35:81-103
70        <meta-data
70-->D:\PROJECTS\main-daksh\AndroidManifest.xml:36:5-99
71            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
71-->D:\PROJECTS\main-daksh\AndroidManifest.xml:36:16-79
72            android:value="0" />
72-->D:\PROJECTS\main-daksh\AndroidManifest.xml:36:80-97
73
74        <activity
74-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:5-54:16
75            android:name="com.cyberself.copilot.MainActivity"
75-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:15-43
76            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
76-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:44-134
77            android:exported="true"
77-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:256-279
78            android:launchMode="singleTask"
78-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:135-166
79            android:screenOrientation="portrait"
79-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:280-316
80            android:theme="@style/Theme.App.SplashScreen"
80-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:210-255
81            android:windowSoftInputMode="adjustResize" >
81-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:167-209
82            <intent-filter>
82-->D:\PROJECTS\main-daksh\AndroidManifest.xml:38:7-41:23
83                <action android:name="android.intent.action.MAIN" />
83-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:9-60
83-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:17-58
84
85                <category android:name="android.intent.category.LAUNCHER" />
85-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:9-68
85-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:19-66
86            </intent-filter>
87            <intent-filter>
87-->D:\PROJECTS\main-daksh\AndroidManifest.xml:42:7-47:23
88                <action android:name="android.intent.action.VIEW" />
88-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:7-58
88-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:15-56
89
90                <category android:name="android.intent.category.DEFAULT" />
90-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:9-67
90-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:19-65
91                <category android:name="android.intent.category.BROWSABLE" />
91-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:7-67
91-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:17-65
92
93                <data android:scheme="myapp" />
93-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:7-37
93-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:13-35
94            </intent-filter>
95            <intent-filter data-generated="true" >
95-->D:\PROJECTS\main-daksh\AndroidManifest.xml:48:7-53:23
95-->D:\PROJECTS\main-daksh\AndroidManifest.xml:48:22-43
96                <action android:name="android.intent.action.VIEW" />
96-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:7-58
96-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:15-56
97
98                <data
98-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:7-37
99                    android:host="alarm"
99-->D:\PROJECTS\main-daksh\AndroidManifest.xml:50:38-58
100                    android:scheme="myapp" />
100-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:13-35
101
102                <category android:name="android.intent.category.BROWSABLE" />
102-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:7-67
102-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:17-65
103                <category android:name="android.intent.category.DEFAULT" />
103-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:9-67
103-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:19-65
104            </intent-filter>
105        </activity>
106        <activity
106-->D:\PROJECTS\main-daksh\AndroidManifest.xml:55:5-62:34
107            android:name="com.cyberself.copilot.AlarmLauncherActivity"
107-->D:\PROJECTS\main-daksh\AndroidManifest.xml:56:7-44
108            android:excludeFromRecents="true"
108-->D:\PROJECTS\main-daksh\AndroidManifest.xml:58:7-40
109            android:exported="false"
109-->D:\PROJECTS\main-daksh\AndroidManifest.xml:62:7-31
110            android:showWhenLocked="true"
110-->D:\PROJECTS\main-daksh\AndroidManifest.xml:60:7-36
111            android:taskAffinity=""
111-->D:\PROJECTS\main-daksh\AndroidManifest.xml:59:7-30
112            android:theme="@android:style/Theme.Translucent.NoTitleBar"
112-->D:\PROJECTS\main-daksh\AndroidManifest.xml:57:7-66
113            android:turnScreenOn="true" />
113-->D:\PROJECTS\main-daksh\AndroidManifest.xml:61:7-34
114        <!-- Register receivers -->
115        <receiver
115-->D:\PROJECTS\main-daksh\AndroidManifest.xml:64:5-68:35
116            android:name="com.cyberself.copilot.AlarmReceiver"
116-->D:\PROJECTS\main-daksh\AndroidManifest.xml:65:9-38
117            android:directBootAware="true"
117-->D:\PROJECTS\main-daksh\AndroidManifest.xml:67:9-39
118            android:enabled="true"
118-->D:\PROJECTS\main-daksh\AndroidManifest.xml:66:9-31
119            android:exported="true" />
119-->D:\PROJECTS\main-daksh\AndroidManifest.xml:68:9-32
120        <receiver
120-->D:\PROJECTS\main-daksh\AndroidManifest.xml:70:5-84:16
121            android:name="com.cyberself.copilot.BootReceiver"
121-->D:\PROJECTS\main-daksh\AndroidManifest.xml:71:9-37
122            android:directBootAware="true"
122-->D:\PROJECTS\main-daksh\AndroidManifest.xml:73:9-39
123            android:enabled="true"
123-->D:\PROJECTS\main-daksh\AndroidManifest.xml:72:9-31
124            android:exported="true"
124-->D:\PROJECTS\main-daksh\AndroidManifest.xml:74:9-32
125            android:priority="999" >
125-->D:\PROJECTS\main-daksh\AndroidManifest.xml:75:9-31
126            <intent-filter>
126-->D:\PROJECTS\main-daksh\AndroidManifest.xml:76:9-83:25
127                <action android:name="android.intent.action.BOOT_COMPLETED" />
127-->D:\PROJECTS\main-daksh\AndroidManifest.xml:77:13-75
127-->D:\PROJECTS\main-daksh\AndroidManifest.xml:77:21-72
128                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
128-->D:\PROJECTS\main-daksh\AndroidManifest.xml:78:13-78
128-->D:\PROJECTS\main-daksh\AndroidManifest.xml:78:21-75
129                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
129-->D:\PROJECTS\main-daksh\AndroidManifest.xml:79:13-78
129-->D:\PROJECTS\main-daksh\AndroidManifest.xml:79:21-75
130                <action android:name="android.intent.action.ACTION_BOOT_COMPLETED" />
130-->D:\PROJECTS\main-daksh\AndroidManifest.xml:80:13-82
130-->D:\PROJECTS\main-daksh\AndroidManifest.xml:80:21-79
131                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
131-->D:\PROJECTS\main-daksh\AndroidManifest.xml:81:13-80
131-->D:\PROJECTS\main-daksh\AndroidManifest.xml:81:21-77
132
133                <category android:name="android.intent.category.DEFAULT" />
133-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:9-67
133-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:19-65
134            </intent-filter>
135        </receiver>
136
137        <!-- Test Activity for alarm testing -->
138        <activity
138-->D:\PROJECTS\main-daksh\AndroidManifest.xml:87:5-96:16
139            android:name="com.cyberself.copilot.TestAlarmActivity"
139-->D:\PROJECTS\main-daksh\AndroidManifest.xml:88:9-42
140            android:enabled="true"
140-->D:\PROJECTS\main-daksh\AndroidManifest.xml:89:9-31
141            android:exported="true"
141-->D:\PROJECTS\main-daksh\AndroidManifest.xml:90:9-32
142            android:theme="@android:style/Theme.Material.Light" >
142-->D:\PROJECTS\main-daksh\AndroidManifest.xml:91:9-60
143            <intent-filter>
143-->D:\PROJECTS\main-daksh\AndroidManifest.xml:38:7-41:23
144                <action android:name="android.intent.action.MAIN" />
144-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:9-60
144-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:17-58
145
146                <category android:name="android.intent.category.LAUNCHER" />
146-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:9-68
146-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:19-66
147            </intent-filter>
148        </activity>
149
150        <!-- Register service -->
151        <service
151-->D:\PROJECTS\main-daksh\AndroidManifest.xml:100:5-107:15
152            android:name="com.cyberself.copilot.AlarmService"
152-->D:\PROJECTS\main-daksh\AndroidManifest.xml:101:9-37
153            android:enabled="true"
153-->D:\PROJECTS\main-daksh\AndroidManifest.xml:102:9-31
154            android:exported="false"
154-->D:\PROJECTS\main-daksh\AndroidManifest.xml:104:9-33
155            android:foregroundServiceType="specialUse" >
155-->D:\PROJECTS\main-daksh\AndroidManifest.xml:103:9-51
156            <property
156-->D:\PROJECTS\main-daksh\AndroidManifest.xml:105:9-106:43
157                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
157-->D:\PROJECTS\main-daksh\AndroidManifest.xml:105:19-78
158                android:value="alarm" />
158-->D:\PROJECTS\main-daksh\AndroidManifest.xml:106:19-40
159        </service>
160
161        <provider
161-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
162            android:name="androidx.startup.InitializationProvider"
162-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
163            android:authorities="com.cyberself.copilot.androidx-startup"
163-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
164            android:exported="false" >
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
165            <meta-data
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
166                android:name="androidx.emoji2.text.EmojiCompatInitializer"
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
167                android:value="androidx.startup" />
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
168            <meta-data
168-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
169                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
169-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
170                android:value="androidx.startup" />
170-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
171        </provider>
172    </application>
173
174</manifest>
