1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.cyberself.copilot"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->D:\PROJECTS\main-daksh\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->D:\PROJECTS\main-daksh\AndroidManifest.xml
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\PROJECTS\main-daksh\AndroidManifest.xml:2:3-64
11-->D:\PROJECTS\main-daksh\AndroidManifest.xml:2:20-62
12    <uses-permission android:name="android.permission.VIBRATE" />
12-->D:\PROJECTS\main-daksh\AndroidManifest.xml:3:3-63
12-->D:\PROJECTS\main-daksh\AndroidManifest.xml:3:20-61
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->D:\PROJECTS\main-daksh\AndroidManifest.xml:4:3-77
13-->D:\PROJECTS\main-daksh\AndroidManifest.xml:4:20-74
14    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- For Android 12+ -->
14-->D:\PROJECTS\main-daksh\AndroidManifest.xml:5:3-72
14-->D:\PROJECTS\main-daksh\AndroidManifest.xml:5:20-69
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\PROJECTS\main-daksh\AndroidManifest.xml:6:3-75
15-->D:\PROJECTS\main-daksh\AndroidManifest.xml:6:20-72
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
16-->D:\PROJECTS\main-daksh\AndroidManifest.xml:7:3-87
16-->D:\PROJECTS\main-daksh\AndroidManifest.xml:7:20-84
17    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
17-->D:\PROJECTS\main-daksh\AndroidManifest.xml:8:3-79
17-->D:\PROJECTS\main-daksh\AndroidManifest.xml:8:20-76
18    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
18-->D:\PROJECTS\main-daksh\AndroidManifest.xml:9:3-74
18-->D:\PROJECTS\main-daksh\AndroidManifest.xml:9:20-71
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\PROJECTS\main-daksh\AndroidManifest.xml:10:3-66
19-->D:\PROJECTS\main-daksh\AndroidManifest.xml:10:20-63
20    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
20-->D:\PROJECTS\main-daksh\AndroidManifest.xml:11:3-77
20-->D:\PROJECTS\main-daksh\AndroidManifest.xml:11:20-75
21    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
21-->D:\PROJECTS\main-daksh\AndroidManifest.xml:12:3-76
21-->D:\PROJECTS\main-daksh\AndroidManifest.xml:12:20-73
22    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
22-->D:\PROJECTS\main-daksh\AndroidManifest.xml:13:3-79
22-->D:\PROJECTS\main-daksh\AndroidManifest.xml:13:20-76
23    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
23-->D:\PROJECTS\main-daksh\AndroidManifest.xml:14:3-71
23-->D:\PROJECTS\main-daksh\AndroidManifest.xml:14:20-68
24    <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
24-->D:\PROJECTS\main-daksh\AndroidManifest.xml:15:3-73
24-->D:\PROJECTS\main-daksh\AndroidManifest.xml:15:20-70
25    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
25-->D:\PROJECTS\main-daksh\AndroidManifest.xml:16:3-77
25-->D:\PROJECTS\main-daksh\AndroidManifest.xml:16:20-75
26    <uses-permission android:name="android.permission.RECORD_AUDIO" />
26-->D:\PROJECTS\main-daksh\AndroidManifest.xml:17:3-68
26-->D:\PROJECTS\main-daksh\AndroidManifest.xml:17:20-66
27    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
27-->D:\PROJECTS\main-daksh\AndroidManifest.xml:18:3-69
27-->D:\PROJECTS\main-daksh\AndroidManifest.xml:18:20-67
28    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
28-->D:\PROJECTS\main-daksh\AndroidManifest.xml:19:3-78
28-->D:\PROJECTS\main-daksh\AndroidManifest.xml:19:20-76
29    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
29-->D:\PROJECTS\main-daksh\AndroidManifest.xml:20:3-71
29-->D:\PROJECTS\main-daksh\AndroidManifest.xml:20:20-69
30    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
30-->D:\PROJECTS\main-daksh\AndroidManifest.xml:21:3-93
30-->D:\PROJECTS\main-daksh\AndroidManifest.xml:21:20-90
31    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
31-->D:\PROJECTS\main-daksh\AndroidManifest.xml:22:3-95
31-->D:\PROJECTS\main-daksh\AndroidManifest.xml:22:20-92
32    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
32-->D:\PROJECTS\main-daksh\AndroidManifest.xml:23:3-73
32-->D:\PROJECTS\main-daksh\AndroidManifest.xml:23:20-70
33    <uses-permission android:name="com.xiaomi.permission.AUTOSTART" />
33-->D:\PROJECTS\main-daksh\AndroidManifest.xml:24:3-69
33-->D:\PROJECTS\main-daksh\AndroidManifest.xml:24:20-66
34    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
34-->D:\PROJECTS\main-daksh\AndroidManifest.xml:25:3-73
34-->D:\PROJECTS\main-daksh\AndroidManifest.xml:25:20-70
35
36    <queries>
36-->D:\PROJECTS\main-daksh\AndroidManifest.xml:26:3-32:13
37        <intent>
37-->D:\PROJECTS\main-daksh\AndroidManifest.xml:27:5-31:14
38            <action android:name="android.intent.action.VIEW" />
38-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:7-58
38-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:15-56
39
40            <category android:name="android.intent.category.BROWSABLE" />
40-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:7-67
40-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:17-65
41
42            <data android:scheme="https" />
42-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:7-37
42-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:13-35
43        </intent>
44    </queries>
45
46    <permission
46-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
47        android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
51
52    <application
52-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:3-108:17
53        android:allowBackup="true"
53-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:130-156
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
55        android:debuggable="true"
56        android:icon="@mipmap/ic_launcher"
56-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:49-83
57        android:label="@string/app_name"
57-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:16-48
58        android:roundIcon="@mipmap/ic_launcher_round"
58-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:84-129
59        android:supportsRtl="true"
59-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:209-235
60        android:theme="@android:style/Theme.Material.Light" >
60-->D:\PROJECTS\main-daksh\AndroidManifest.xml:33:157-208
61        <meta-data
61-->D:\PROJECTS\main-daksh\AndroidManifest.xml:34:5-83
62            android:name="expo.modules.updates.ENABLED"
62-->D:\PROJECTS\main-daksh\AndroidManifest.xml:34:16-59
63            android:value="false" />
63-->D:\PROJECTS\main-daksh\AndroidManifest.xml:34:60-81
64        <meta-data
64-->D:\PROJECTS\main-daksh\AndroidManifest.xml:35:5-105
65            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
65-->D:\PROJECTS\main-daksh\AndroidManifest.xml:35:16-80
66            android:value="ALWAYS" />
66-->D:\PROJECTS\main-daksh\AndroidManifest.xml:35:81-103
67        <meta-data
67-->D:\PROJECTS\main-daksh\AndroidManifest.xml:36:5-99
68            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
68-->D:\PROJECTS\main-daksh\AndroidManifest.xml:36:16-79
69            android:value="0" />
69-->D:\PROJECTS\main-daksh\AndroidManifest.xml:36:80-97
70
71        <activity
71-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:5-54:16
72            android:name="com.cyberself.copilot.MainActivity"
72-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:15-43
73            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
73-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:44-134
74            android:exported="true"
74-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:256-279
75            android:launchMode="singleTask"
75-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:135-166
76            android:screenOrientation="portrait"
76-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:280-316
77            android:theme="@style/Theme.App.SplashScreen"
77-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:210-255
78            android:windowSoftInputMode="adjustResize" >
78-->D:\PROJECTS\main-daksh\AndroidManifest.xml:37:167-209
79            <intent-filter>
79-->D:\PROJECTS\main-daksh\AndroidManifest.xml:38:7-41:23
80                <action android:name="android.intent.action.MAIN" />
80-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:9-60
80-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:17-58
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:9-68
82-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:19-66
83            </intent-filter>
84            <intent-filter>
84-->D:\PROJECTS\main-daksh\AndroidManifest.xml:42:7-47:23
85                <action android:name="android.intent.action.VIEW" />
85-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:7-58
85-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:15-56
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:9-67
87-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:19-65
88                <category android:name="android.intent.category.BROWSABLE" />
88-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:7-67
88-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:17-65
89
90                <data android:scheme="myapp" />
90-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:7-37
90-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:13-35
91            </intent-filter>
92            <intent-filter data-generated="true" >
92-->D:\PROJECTS\main-daksh\AndroidManifest.xml:48:7-53:23
92-->D:\PROJECTS\main-daksh\AndroidManifest.xml:48:22-43
93                <action android:name="android.intent.action.VIEW" />
93-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:7-58
93-->D:\PROJECTS\main-daksh\AndroidManifest.xml:28:15-56
94
95                <data
95-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:7-37
96                    android:host="alarm"
96-->D:\PROJECTS\main-daksh\AndroidManifest.xml:50:38-58
97                    android:scheme="myapp" />
97-->D:\PROJECTS\main-daksh\AndroidManifest.xml:30:13-35
98
99                <category android:name="android.intent.category.BROWSABLE" />
99-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:7-67
99-->D:\PROJECTS\main-daksh\AndroidManifest.xml:29:17-65
100                <category android:name="android.intent.category.DEFAULT" />
100-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:9-67
100-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:19-65
101            </intent-filter>
102        </activity>
103        <activity
103-->D:\PROJECTS\main-daksh\AndroidManifest.xml:55:5-62:34
104            android:name="com.cyberself.copilot.AlarmLauncherActivity"
104-->D:\PROJECTS\main-daksh\AndroidManifest.xml:56:7-44
105            android:excludeFromRecents="true"
105-->D:\PROJECTS\main-daksh\AndroidManifest.xml:58:7-40
106            android:exported="false"
106-->D:\PROJECTS\main-daksh\AndroidManifest.xml:62:7-31
107            android:showWhenLocked="true"
107-->D:\PROJECTS\main-daksh\AndroidManifest.xml:60:7-36
108            android:taskAffinity=""
108-->D:\PROJECTS\main-daksh\AndroidManifest.xml:59:7-30
109            android:theme="@android:style/Theme.Translucent.NoTitleBar"
109-->D:\PROJECTS\main-daksh\AndroidManifest.xml:57:7-66
110            android:turnScreenOn="true" />
110-->D:\PROJECTS\main-daksh\AndroidManifest.xml:61:7-34
111        <!-- Register receivers -->
112        <receiver
112-->D:\PROJECTS\main-daksh\AndroidManifest.xml:64:5-68:35
113            android:name="com.cyberself.copilot.AlarmReceiver"
113-->D:\PROJECTS\main-daksh\AndroidManifest.xml:65:9-38
114            android:directBootAware="true"
114-->D:\PROJECTS\main-daksh\AndroidManifest.xml:67:9-39
115            android:enabled="true"
115-->D:\PROJECTS\main-daksh\AndroidManifest.xml:66:9-31
116            android:exported="true" />
116-->D:\PROJECTS\main-daksh\AndroidManifest.xml:68:9-32
117        <receiver
117-->D:\PROJECTS\main-daksh\AndroidManifest.xml:70:5-84:16
118            android:name="com.cyberself.copilot.BootReceiver"
118-->D:\PROJECTS\main-daksh\AndroidManifest.xml:71:9-37
119            android:directBootAware="true"
119-->D:\PROJECTS\main-daksh\AndroidManifest.xml:73:9-39
120            android:enabled="true"
120-->D:\PROJECTS\main-daksh\AndroidManifest.xml:72:9-31
121            android:exported="true"
121-->D:\PROJECTS\main-daksh\AndroidManifest.xml:74:9-32
122            android:priority="999" >
122-->D:\PROJECTS\main-daksh\AndroidManifest.xml:75:9-31
123            <intent-filter>
123-->D:\PROJECTS\main-daksh\AndroidManifest.xml:76:9-83:25
124                <action android:name="android.intent.action.BOOT_COMPLETED" />
124-->D:\PROJECTS\main-daksh\AndroidManifest.xml:77:13-75
124-->D:\PROJECTS\main-daksh\AndroidManifest.xml:77:21-72
125                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
125-->D:\PROJECTS\main-daksh\AndroidManifest.xml:78:13-78
125-->D:\PROJECTS\main-daksh\AndroidManifest.xml:78:21-75
126                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
126-->D:\PROJECTS\main-daksh\AndroidManifest.xml:79:13-78
126-->D:\PROJECTS\main-daksh\AndroidManifest.xml:79:21-75
127                <action android:name="android.intent.action.ACTION_BOOT_COMPLETED" />
127-->D:\PROJECTS\main-daksh\AndroidManifest.xml:80:13-82
127-->D:\PROJECTS\main-daksh\AndroidManifest.xml:80:21-79
128                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
128-->D:\PROJECTS\main-daksh\AndroidManifest.xml:81:13-80
128-->D:\PROJECTS\main-daksh\AndroidManifest.xml:81:21-77
129
130                <category android:name="android.intent.category.DEFAULT" />
130-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:9-67
130-->D:\PROJECTS\main-daksh\AndroidManifest.xml:44:19-65
131            </intent-filter>
132        </receiver>
133
134        <!-- Test Activity for alarm testing -->
135        <activity
135-->D:\PROJECTS\main-daksh\AndroidManifest.xml:87:5-96:16
136            android:name="com.cyberself.copilot.TestAlarmActivity"
136-->D:\PROJECTS\main-daksh\AndroidManifest.xml:88:9-42
137            android:enabled="true"
137-->D:\PROJECTS\main-daksh\AndroidManifest.xml:89:9-31
138            android:exported="true"
138-->D:\PROJECTS\main-daksh\AndroidManifest.xml:90:9-32
139            android:theme="@android:style/Theme.Material.Light" >
139-->D:\PROJECTS\main-daksh\AndroidManifest.xml:91:9-60
140            <intent-filter>
140-->D:\PROJECTS\main-daksh\AndroidManifest.xml:38:7-41:23
141                <action android:name="android.intent.action.MAIN" />
141-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:9-60
141-->D:\PROJECTS\main-daksh\AndroidManifest.xml:39:17-58
142
143                <category android:name="android.intent.category.LAUNCHER" />
143-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:9-68
143-->D:\PROJECTS\main-daksh\AndroidManifest.xml:40:19-66
144            </intent-filter>
145        </activity>
146
147        <!-- Register service -->
148        <service
148-->D:\PROJECTS\main-daksh\AndroidManifest.xml:100:5-107:15
149            android:name="com.cyberself.copilot.AlarmService"
149-->D:\PROJECTS\main-daksh\AndroidManifest.xml:101:9-37
150            android:enabled="true"
150-->D:\PROJECTS\main-daksh\AndroidManifest.xml:102:9-31
151            android:exported="false"
151-->D:\PROJECTS\main-daksh\AndroidManifest.xml:104:9-33
152            android:foregroundServiceType="specialUse" >
152-->D:\PROJECTS\main-daksh\AndroidManifest.xml:103:9-51
153            <property
153-->D:\PROJECTS\main-daksh\AndroidManifest.xml:105:9-106:43
154                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
154-->D:\PROJECTS\main-daksh\AndroidManifest.xml:105:19-78
155                android:value="alarm" />
155-->D:\PROJECTS\main-daksh\AndroidManifest.xml:106:19-40
156        </service>
157
158        <provider
158-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
159            android:name="androidx.startup.InitializationProvider"
159-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
160            android:authorities="com.cyberself.copilot.androidx-startup"
160-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
161            android:exported="false" >
161-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
162            <meta-data
162-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
163                android:name="androidx.emoji2.text.EmojiCompatInitializer"
163-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
164                android:value="androidx.startup" />
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
165            <meta-data
165-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
166                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
166-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
167                android:value="androidx.startup" />
167-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
168        </provider>
169    </application>
170
171</manifest>
