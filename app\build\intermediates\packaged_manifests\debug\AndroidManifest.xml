<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.cyberself.copilot"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
    <uses-permission android:name="android.permission.USE_EXACT_ALARM" /> <!-- For Android 12+ -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.QUICKBOOT_POWERON" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <uses-permission android:name="android.permission.TURN_SCREEN_ON" />
    <uses-permission android:name="android.permission.SHOW_WHEN_LOCKED" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="com.huawei.permission.external_app_settings.USE_COMPONENT" />
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
    <uses-permission android:name="com.xiaomi.permission.AUTOSTART" />
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />

    <queries>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <category android:name="android.intent.category.BROWSABLE" />

            <data android:scheme="https" />
        </intent>
    </queries>

    <permission
        android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.Material.Light" >
        <meta-data
            android:name="expo.modules.updates.ENABLED"
            android:value="false" />
        <meta-data
            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
            android:value="ALWAYS" />
        <meta-data
            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
            android:value="0" />

        <activity
            android:name="com.cyberself.copilot.MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.App.SplashScreen"
            android:windowSoftInputMode="adjustResize" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="myapp" />
            </intent-filter>
            <intent-filter data-generated="true" >
                <action android:name="android.intent.action.VIEW" />

                <data
                    android:host="alarm"
                    android:scheme="myapp" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.cyberself.copilot.AlarmLauncherActivity"
            android:excludeFromRecents="true"
            android:exported="false"
            android:showWhenLocked="true"
            android:taskAffinity=""
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:turnScreenOn="true" />
        <!-- Register receivers -->
        <receiver
            android:name="com.cyberself.copilot.AlarmReceiver"
            android:directBootAware="true"
            android:enabled="true"
            android:exported="true" />
        <receiver
            android:name="com.cyberself.copilot.BootReceiver"
            android:directBootAware="true"
            android:enabled="true"
            android:exported="true"
            android:priority="999" >
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
                <action android:name="android.intent.action.ACTION_BOOT_COMPLETED" />
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!-- Test Activity for alarm testing -->
        <activity
            android:name="com.cyberself.copilot.TestAlarmActivity"
            android:enabled="true"
            android:exported="true"
            android:theme="@android:style/Theme.Material.Light" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Register service -->
        <service
            android:name="com.cyberself.copilot.AlarmService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="specialUse" >
            <property
                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
                android:value="alarm" />
        </service>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.cyberself.copilot.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>