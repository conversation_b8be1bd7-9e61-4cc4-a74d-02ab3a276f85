<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\main-daksh\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\main-daksh\res"><file name="ic_launcher_background" path="D:\PROJECTS\main-daksh\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="notification_icon" path="D:\PROJECTS\main-daksh\res\drawable\notification_icon.xml" qualifiers="" type="drawable"/><file name="rn_edit_text_material" path="D:\PROJECTS\main-daksh\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="splashscreen_logo" path="D:\PROJECTS\main-daksh\res\drawable-hdpi\splashscreen_logo.png" qualifiers="hdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\PROJECTS\main-daksh\res\drawable-mdpi\splashscreen_logo.png" qualifiers="mdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\PROJECTS\main-daksh\res\drawable-xhdpi\splashscreen_logo.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\PROJECTS\main-daksh\res\drawable-xxhdpi\splashscreen_logo.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splashscreen_logo" path="D:\PROJECTS\main-daksh\res\drawable-xxxhdpi\splashscreen_logo.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="D:\PROJECTS\main-daksh\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\PROJECTS\main-daksh\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\PROJECTS\main-daksh\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PROJECTS\main-daksh\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PROJECTS\main-daksh\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PROJECTS\main-daksh\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PROJECTS\main-daksh\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PROJECTS\main-daksh\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PROJECTS\main-daksh\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PROJECTS\main-daksh\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PROJECTS\main-daksh\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PROJECTS\main-daksh\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PROJECTS\main-daksh\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PROJECTS\main-daksh\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\PROJECTS\main-daksh\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\PROJECTS\main-daksh\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\PROJECTS\main-daksh\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\PROJECTS\main-daksh\res\values\colors.xml" qualifiers=""><color name="splashscreen_background">#ffffff</color><color name="iconBackground">#ffffff</color><color name="colorPrimary">#023c69</color><color name="colorPrimaryDark">#ffffff</color></file><file path="D:\PROJECTS\main-daksh\res\values\strings.xml" qualifiers=""><string name="app_name">copilot-ragapp</string><string name="expo_system_ui_user_interface_style" translatable="false">automatic</string><string name="expo_splash_screen_resize_mode" translatable="false">contain</string><string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string></file><file path="D:\PROJECTS\main-daksh\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style><style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style><style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style></file><file path="D:\PROJECTS\main-daksh\res\values-night\colors.xml" qualifiers="night-v8"/><file name="secure_store_backup_rules" path="D:\PROJECTS\main-daksh\res\xml\secure_store_backup_rules.xml" qualifiers="" type="xml"/><file name="secure_store_data_extraction_rules" path="D:\PROJECTS\main-daksh\res\xml\secure_store_data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\main-daksh\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\main-daksh\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\main-daksh\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\main-daksh\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>