[{"merged": "com.cyberself.copilot.app-merged_res-21:/xml_secure_store_backup_rules.xml.flat", "source": "com.cyberself.copilot.app-main-daksh-23:/xml/secure_store_backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable_notification_icon.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable\\notification_icon.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable-mdpi_splashscreen_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable-mdpi\\splashscreen_logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable_rn_edit_text_material.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable\\rn_edit_text_material.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable-hdpi_splashscreen_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable-hdpi\\splashscreen_logo.png"}, {"merged": "com.cyberself.copilot.app-merged_res-21:/xml_secure_store_data_extraction_rules.xml.flat", "source": "com.cyberself.copilot.app-main-daksh-23:/xml/secure_store_data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable-xhdpi_splashscreen_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable-xhdpi\\splashscreen_logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable-xxxhdpi_splashscreen_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable-xxxhdpi\\splashscreen_logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\drawable-xxhdpi_splashscreen_logo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\drawable-xxhdpi\\splashscreen_logo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-merged_res-21:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.10\\com.cyberself.copilot.app-main-daksh-23:\\mipmap-hdpi\\ic_launcher.webp"}]