{"logs": [{"outputFile": "com.cyberself.copilot.app-mergeDebugResources-26:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\7480a749a67c0291f39bb6131bf08483\\transformed\\material-1.8.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,25,28,31,34,37,40,43,46,49,52,55,56,59,64,75,81,91,101,111,121,131,141,151,161,171,181,191,201,211,221,231,237,243,249,255,259,263,264,265,266,270,273,276,279,282,283,286,289,293,297", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,345,443,511,590,678,766,854,942,1029,1116,1203,1290,1383,1490,1595,1714,1839,1960,2173,2432,2703,2921,3153,3389,3639,3852,4061,4292,4493,4609,4779,5100,6129,6586,7137,7692,8248,8809,9361,9912,10464,11017,11566,12119,12675,13230,13776,14330,14885,15177,15471,15771,16071,16400,16741,16879,17023,17179,17572,17790,18012,18238,18454,18564,18734,18924,19165,19424", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,24,27,30,33,36,39,42,45,48,51,54,55,58,63,74,80,90,100,110,120,130,140,150,160,170,180,190,200,210,220,230,236,242,248,254,258,262,263,264,265,269,272,275,278,281,282,285,288,292,296,299", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "148,244,340,438,506,585,673,761,849,937,1024,1111,1198,1285,1378,1485,1590,1709,1834,1955,2168,2427,2698,2916,3148,3384,3634,3847,4056,4287,4488,4604,4774,5095,6124,6581,7132,7687,8243,8804,9356,9907,10459,11012,11561,12114,12670,13225,13771,14325,14880,15172,15466,15766,16066,16395,16736,16874,17018,17174,17567,17785,18007,18233,18449,18559,18729,18919,19160,19419,19596"}, "to": {"startLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,80,81,82,83,85,86,87,90,93,188,191,194,197,203,206,209,276,279,280,283,288,299,347,357,367,377,387,397,407,417,427,437,447,457,467,477,487,497,503,509,515,521,525,529,530,531,532,536,539,542,545,556,557,560,563,567,571", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "173,271,367,463,561,629,708,796,884,972,1060,1147,1234,1321,6970,7063,7170,7275,7497,7622,7743,7956,8215,14396,14614,14846,15082,15531,15744,15953,20740,20941,21057,21227,21548,22577,25682,26233,26788,27344,27905,28457,29008,29560,30113,30662,31215,31771,32326,32872,33426,33981,34273,34567,34867,35167,35496,35837,35975,36119,36275,36668,36886,37108,37334,38074,38184,38354,38544,38785,39044", "endLines": "3,4,5,6,7,8,9,10,11,12,13,14,15,16,80,81,82,83,85,86,89,92,95,190,193,196,199,205,208,211,278,279,282,287,298,304,356,366,376,386,396,406,416,426,436,446,456,466,476,486,496,502,508,514,520,524,528,529,530,531,535,538,541,544,547,556,559,562,566,570,573", "endColumns": "97,95,95,97,67,78,87,87,87,87,86,86,86,86,92,106,104,118,124,120,10,10,10,10,10,10,10,10,10,10,10,115,10,12,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,137,143,155,10,10,10,10,10,109,10,10,10,10,10", "endOffsets": "266,362,458,556,624,703,791,879,967,1055,1142,1229,1316,1403,7058,7165,7270,7389,7617,7738,7951,8210,8481,14609,14841,15077,15327,15739,15948,16179,20936,21052,21222,21543,22572,23029,26228,26783,27339,27900,28452,29003,29555,30108,30657,31210,31766,32321,32867,33421,33976,34268,34562,34862,35162,35491,35832,35970,36114,36270,36663,36881,37103,37329,37545,38179,38349,38539,38780,39039,39216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\203695e3078906732e64959d18a4a4b1\\transformed\\core-1.9.0\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,17,18,19,343,344,345,346,548,551", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,1408,1472,1539,25186,25302,25428,25554,37550,37722", "endLines": "2,17,18,19,343,344,345,346,550,555", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,1467,1534,1598,25297,25423,25549,25677,37717,38069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\6dcf52ae5421d9e678985198d2b2979b\\transformed\\appcompat-1.6.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,17,19,20,21,22,24,26,27,28,29,30,32,34,36,38,40,42,43,48,50,52,53,54,56,58,59,60,61,62,63,106,109,152,155,158,160,162,164,167,171,174,175,176,179,180,181,182,183,184,187,188,190,192,194,196,200,202,203,204,205,207,211,213,215,216,217,218,219,220,222,223,224,234,235,236,248", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,146,249,352,457,564,673,782,891,1000,1109,1216,1319,1438,1593,1748,1853,1974,2075,2222,2363,2466,2585,2692,2795,2950,3121,3270,3435,3592,3743,3862,4213,4362,4511,4623,4770,4923,5070,5145,5234,5321,5422,5525,8283,8468,11238,11435,11634,11757,11880,11993,12176,12431,12632,12721,12832,13065,13166,13261,13384,13513,13630,13807,13906,14041,14184,14319,14438,14639,14758,14851,14962,15018,15125,15320,15431,15564,15659,15750,15841,15934,16051,16190,16261,16344,16967,17024,17082,17706", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,16,18,19,20,21,23,25,26,27,28,29,31,33,35,37,39,41,42,47,49,51,52,53,55,57,58,59,60,61,62,105,108,151,154,157,159,161,163,166,170,173,174,175,178,179,180,181,182,183,186,187,189,191,193,195,199,201,202,203,204,206,210,212,214,215,216,217,218,219,221,222,223,233,234,235,247,259", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "141,244,347,452,559,668,777,886,995,1104,1211,1314,1433,1588,1743,1848,1969,2070,2217,2358,2461,2580,2687,2790,2945,3116,3265,3430,3587,3738,3857,4208,4357,4506,4618,4765,4918,5065,5140,5229,5316,5417,5520,8278,8463,11233,11430,11629,11752,11875,11988,12171,12426,12627,12716,12827,13060,13161,13256,13379,13508,13625,13802,13901,14036,14179,14314,14433,14634,14753,14846,14957,15013,15120,15315,15426,15559,15654,15745,15836,15929,16046,16185,16256,16339,16962,17019,17077,17701,18337"}, "to": {"startLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,33,35,37,38,39,40,42,44,45,46,47,48,50,52,54,56,58,60,61,66,68,70,71,72,74,76,77,78,79,84,96,139,142,185,200,212,214,216,218,221,225,228,229,230,233,234,235,236,237,238,241,242,244,246,248,250,254,256,257,258,259,261,265,267,269,270,271,272,273,274,305,306,307,317,318,319,331", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1603,1694,1797,1900,2005,2112,2221,2330,2439,2548,2657,2764,2867,2986,3141,3296,3401,3522,3623,3770,3911,4014,4133,4240,4343,4498,4669,4818,4983,5140,5291,5410,5761,5910,6059,6171,6318,6471,6618,6693,6782,6869,7394,8486,11244,11429,14199,15332,16184,16307,16430,16543,16726,16981,17182,17271,17382,17615,17716,17811,17934,18063,18180,18357,18456,18591,18734,18869,18988,19189,19308,19401,19512,19568,19675,19870,19981,20114,20209,20300,20391,20484,20601,23034,23105,23188,23811,23868,23926,24550", "endLines": "20,21,22,23,24,25,26,27,28,29,30,31,32,34,36,37,38,39,41,43,44,45,46,47,49,51,53,55,57,59,60,65,67,69,70,71,73,75,76,77,78,79,84,138,141,184,187,202,213,215,217,220,224,227,228,229,232,233,234,235,236,237,240,241,243,245,247,249,253,255,256,257,258,260,264,266,268,269,270,271,272,273,275,305,306,316,317,318,330,342", "endColumns": "90,102,102,104,106,108,108,108,108,108,106,102,118,12,12,104,120,100,12,12,102,118,106,102,12,12,12,12,12,12,118,12,12,12,111,146,12,12,74,88,86,100,102,12,12,12,12,12,12,12,12,12,12,12,88,110,12,100,94,122,128,116,12,98,12,12,12,12,12,12,92,110,55,12,12,12,12,94,90,90,92,116,12,70,82,12,56,57,12,12", "endOffsets": "1689,1792,1895,2000,2107,2216,2325,2434,2543,2652,2759,2862,2981,3136,3291,3396,3517,3618,3765,3906,4009,4128,4235,4338,4493,4664,4813,4978,5135,5286,5405,5756,5905,6054,6166,6313,6466,6613,6688,6777,6864,6965,7492,11239,11424,14194,14391,15526,16302,16425,16538,16721,16976,17177,17266,17377,17610,17711,17806,17929,18058,18175,18352,18451,18586,18729,18864,18983,19184,19303,19396,19507,19563,19670,19865,19976,20109,20204,20295,20386,20479,20596,20735,23100,23183,23806,23863,23921,24545,25181"}}]}]}