apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'

android {
    namespace 'com.cyberself.copilot'
    compileSdkVersion 34
    buildToolsVersion "34.0.0"

    defaultConfig {
        applicationId "com.cyberself.copilot"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
        }
        debug {
            debuggable true
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main {
            manifest.srcFile '../AndroidManifest.xml'
            java.srcDirs = ['../java']
            res.srcDirs = ['../res']
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core-splashscreen:1.0.0'

    // React Native dependencies
    implementation 'com.facebook.react:react-native:+' // From node_modules
    implementation 'com.facebook.flipper:flipper:0.125.0'
    implementation 'com.facebook.flipper:flipper-noop:0.125.0'
    implementation 'com.facebook.soloader:soloader:0.10.5'

    // Expo modules
    implementation 'host.exp.exponent:expo-modules-core:1.1.0'
    implementation 'host.exp.exponent:expo-updates:0.10.0'
}
