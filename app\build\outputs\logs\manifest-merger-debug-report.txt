-- Merging decision tree log ---
manifest
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\90fd906e82ef7e947f89a8c958ba3354\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6dcf52ae5421d9e678985198d2b2979b\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\b902559e2a5526f28e0ef3185c442d2f\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6f0c0113ffd764d439e86b3e7de3a08a\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3049c1baa866104bf9cd9d232f3b8521\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c315f72f60bcdfee537851fc5f61d795\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c939c08128c1db84147fbc5c443e4620\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\75a323cd820b219e55e7cef15cc2dd47\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b31f55822ac36a98c01ae40a59299539\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9b530fc9ff2fba265d46b49461f4e1dd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\83375a783fcd073a58cd9bd38407b3c4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c49cf586a9303eba41d6c3918ea27eca\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1d23c220d7f420e99f00308a5bff9122\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a2975ba3ff580dae0d27b2c326a2bb18\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9e0f8c5c01474dac9a9c9809b985e66b\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\77b141da315c674076129304e1f1d894\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9bd15f633a922479bfba10f2adcc0a1\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5b8622c24d1e5da3024d11de7dbc94dc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bdf7af7e586d91a29fa3a52ead58090c\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d85fdfa5edbed2100a37555f8b46cd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\712eefaf3001c1be51bcf19a03979bd3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2921b688e711d47d8ccc04c1f0e51ca4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\175cfdaa6e694921c43ea89f9e3da42d\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eeca13d33c1926f59980e4e78191304f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\531fe3f073dcf1c93cfa719383a69b72\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\096f6a68ff6d2ee03dc9a5f9a2a4f0c3\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
	package
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
	android:versionName
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
	android:versionCode
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:1-109:12
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
	xmlns:android
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:2:3-64
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:2:20-62
uses-permission#android.permission.VIBRATE
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:3:3-63
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:3:20-61
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:4:3-77
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:4:20-74
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:5:3-72
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:5:20-69
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:6:3-75
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:6:20-72
uses-permission#android.permission.FOREGROUND_SERVICE_SPECIAL_USE
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:7:3-87
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:7:20-84
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:8:3-79
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:8:20-76
uses-permission#android.permission.QUICKBOOT_POWERON
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:9:3-74
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:9:20-71
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:10:3-66
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:10:20-63
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:11:3-77
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:11:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:12:3-76
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:12:20-73
uses-permission#android.permission.USE_FULL_SCREEN_INTENT
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:13:3-79
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:13:20-76
uses-permission#android.permission.TURN_SCREEN_ON
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:14:3-71
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:14:20-68
uses-permission#android.permission.SHOW_WHEN_LOCKED
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:15:3-73
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:15:20-70
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:16:3-77
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:16:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:17:3-68
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:17:20-66
uses-permission#android.permission.USE_BIOMETRIC
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:18:3-69
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:18:20-67
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:19:3-78
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:19:20-76
uses-permission#android.permission.USE_FINGERPRINT
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:20:3-71
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:20:20-69
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:21:3-93
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:21:20-90
uses-permission#com.huawei.permission.external_app_settings.USE_COMPONENT
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:22:3-95
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:22:20-92
uses-permission#oppo.permission.OPPO_COMPONENT_SAFE
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:23:3-73
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:23:20-70
uses-permission#com.xiaomi.permission.AUTOSTART
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:24:3-69
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:24:20-66
uses-permission#android.permission.DISABLE_KEYGUARD
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:25:3-73
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:25:20-70
queries
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:26:3-32:13
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:27:5-31:14
action#android.intent.action.VIEW
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:28:7-58
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:28:15-56
category#android.intent.category.BROWSABLE
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:29:7-67
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:29:17-65
data
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:30:7-37
	android:host
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:50:38-58
	android:scheme
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:30:13-35
application
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:3-108:17
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d85fdfa5edbed2100a37555f8b46cd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d85fdfa5edbed2100a37555f8b46cd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\712eefaf3001c1be51bcf19a03979bd3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\712eefaf3001c1be51bcf19a03979bd3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:221-247
	android:label
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:48-80
	android:fullBackupContent
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:248-306
	android:roundIcon
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:116-161
	android:icon
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:81-115
	android:allowBackup
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:162-188
	android:theme
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:189-220
	android:dataExtractionRules
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:307-376
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:33:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:34:5-83
	android:value
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:34:60-81
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:34:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:35:5-105
	android:value
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:35:81-103
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:35:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:36:5-99
	android:value
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:36:80-97
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:36:16-79
activity#com.cyberself.copilot.MainActivity
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:5-54:16
	android:screenOrientation
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:280-316
	android:launchMode
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:135-166
	android:windowSoftInputMode
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:167-209
	android:exported
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:256-279
	android:configChanges
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:44-134
	android:theme
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:210-255
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:37:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:38:7-41:23
action#android.intent.action.MAIN
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:39:9-60
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:39:17-58
category#android.intent.category.LAUNCHER
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:40:9-68
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:40:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:myapp
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:42:7-47:23
category#android.intent.category.DEFAULT
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:44:9-67
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:44:19-65
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:alarm+data:scheme:myapp
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:48:7-53:23
	data-generated
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:48:22-43
activity#com.cyberself.copilot.AlarmLauncherActivity
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:55:5-62:34
	android:turnScreenOn
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:61:7-34
	android:excludeFromRecents
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:58:7-40
	android:showWhenLocked
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:60:7-36
	android:exported
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:62:7-31
	android:theme
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:57:7-66
	android:taskAffinity
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:59:7-30
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:56:7-44
receiver#com.cyberself.copilot.AlarmReceiver
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:64:5-68:35
	android:enabled
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:66:9-31
	android:exported
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:68:9-32
	android:directBootAware
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:67:9-39
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:65:9-38
receiver#com.cyberself.copilot.BootReceiver
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:70:5-84:16
	android:enabled
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:72:9-31
	android:exported
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:74:9-32
	android:priority
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:75:9-31
	android:directBootAware
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:73:9-39
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:71:9-37
intent-filter#action:name:android.intent.action.ACTION_BOOT_COMPLETED+action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:com.htc.intent.action.QUICKBOOT_POWERON+category:name:android.intent.category.DEFAULT
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:76:9-83:25
action#android.intent.action.BOOT_COMPLETED
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:77:13-75
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:77:21-72
action#android.intent.action.QUICKBOOT_POWERON
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:78:13-78
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:78:21-75
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:79:13-78
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:79:21-75
action#android.intent.action.ACTION_BOOT_COMPLETED
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:80:13-82
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:80:21-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:81:13-80
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:81:21-77
activity#com.cyberself.copilot.TestAlarmActivity
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:87:5-96:16
	android:enabled
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:89:9-31
	android:exported
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:90:9-32
	android:theme
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:91:9-60
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:88:9-42
service#com.cyberself.copilot.AlarmService
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:100:5-107:15
	android:enabled
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:102:9-31
	android:exported
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:104:9-33
	android:foregroundServiceType
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:103:9-51
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:101:9-37
property
ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:105:9-106:43
	android:value
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:106:19-40
	android:name
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml:105:19-78
uses-sdk
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\90fd906e82ef7e947f89a8c958ba3354\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\90fd906e82ef7e947f89a8c958ba3354\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6dcf52ae5421d9e678985198d2b2979b\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6dcf52ae5421d9e678985198d2b2979b\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\b902559e2a5526f28e0ef3185c442d2f\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\8.10\transforms\b902559e2a5526f28e0ef3185c442d2f\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6f0c0113ffd764d439e86b3e7de3a08a\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\6f0c0113ffd764d439e86b3e7de3a08a\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3049c1baa866104bf9cd9d232f3b8521\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\3049c1baa866104bf9cd9d232f3b8521\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c315f72f60bcdfee537851fc5f61d795\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c315f72f60bcdfee537851fc5f61d795\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c939c08128c1db84147fbc5c443e4620\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\c939c08128c1db84147fbc5c443e4620\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\75a323cd820b219e55e7cef15cc2dd47\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\75a323cd820b219e55e7cef15cc2dd47\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b31f55822ac36a98c01ae40a59299539\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\b31f55822ac36a98c01ae40a59299539\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9b530fc9ff2fba265d46b49461f4e1dd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9b530fc9ff2fba265d46b49461f4e1dd\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\83375a783fcd073a58cd9bd38407b3c4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\83375a783fcd073a58cd9bd38407b3c4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c49cf586a9303eba41d6c3918ea27eca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\c49cf586a9303eba41d6c3918ea27eca\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1d23c220d7f420e99f00308a5bff9122\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\1d23c220d7f420e99f00308a5bff9122\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a2975ba3ff580dae0d27b2c326a2bb18\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a2975ba3ff580dae0d27b2c326a2bb18\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9e0f8c5c01474dac9a9c9809b985e66b\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\9e0f8c5c01474dac9a9c9809b985e66b\transformed\jetified-core-splashscreen-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\77b141da315c674076129304e1f1d894\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\77b141da315c674076129304e1f1d894\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9bd15f633a922479bfba10f2adcc0a1\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\a9bd15f633a922479bfba10f2adcc0a1\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5b8622c24d1e5da3024d11de7dbc94dc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\5b8622c24d1e5da3024d11de7dbc94dc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bdf7af7e586d91a29fa3a52ead58090c\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\bdf7af7e586d91a29fa3a52ead58090c\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d85fdfa5edbed2100a37555f8b46cd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\6d85fdfa5edbed2100a37555f8b46cd7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\712eefaf3001c1be51bcf19a03979bd3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\712eefaf3001c1be51bcf19a03979bd3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2921b688e711d47d8ccc04c1f0e51ca4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\2921b688e711d47d8ccc04c1f0e51ca4\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\175cfdaa6e694921c43ea89f9e3da42d\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\175cfdaa6e694921c43ea89f9e3da42d\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eeca13d33c1926f59980e4e78191304f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\eeca13d33c1926f59980e4e78191304f\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\531fe3f073dcf1c93cfa719383a69b72\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\531fe3f073dcf1c93cfa719383a69b72\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\096f6a68ff6d2ee03dc9a5f9a2a4f0c3\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\096f6a68ff6d2ee03dc9a5f9a2a4f0c3\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
		ADDED from D:\PROJECTS\main-daksh\AndroidManifest.xml
		INJECTED from D:\PROJECTS\main-daksh\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\712eefaf3001c1be51bcf19a03979bd3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\712eefaf3001c1be51bcf19a03979bd3\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\bd54694634a1383b0ff301096f347436\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.cyberself.copilot.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.10\transforms\203695e3078906732e64959d18a4a4b1\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.10\transforms\cf06d5058e7005ebd3bc141aca6b4b0d\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
