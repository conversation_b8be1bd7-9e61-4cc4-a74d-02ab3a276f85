package com.cyberself.copilot
import expo.modules.splashscreen.SplashScreenManager

import android.os.Build
import android.os.Bundle
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.app.NotificationManager
import android.app.NotificationChannel
import android.content.Context
import android.content.SharedPreferences
import android.app.KeyguardManager
import android.view.WindowManager
import android.app.Notification

import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint.fabricEnabled
import com.facebook.react.defaults.DefaultReactActivityDelegate

import expo.modules.ReactActivityDelegateWrapper
import com.facebook.react.ReactInstanceManager
import com.facebook.react.bridge.ReactContext
import com.facebook.react.modules.core.DeviceEventManagerModule

class MainActivity : ReactActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        // Set the theme to AppTheme BEFORE onCreate
        SplashScreenManager.registerOnActivity(this)
        super.onCreate(null)
        
        // Create notification channel early
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                "alarm_channel_fullscreen",
                "Alarm Notifications",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Alarm notifications with full screen"
                setBypassDnd(true)
                lockscreenVisibility = Notification.VISIBILITY_PUBLIC
            }
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
        
        // Check if this is an alarm launch
        if (intent.getBooleanExtra("LAUNCH_ALARM_SCREEN", false)) {
            setupAlarmWindowFlags()
        }
        
        handleAlarmIntent(intent)
    }

    private fun setupAlarmWindowFlags() {
        // Turn screen on and show activity over lock screen
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            setShowWhenLocked(true)
            setTurnScreenOn(true)
            
            // For Android 10+, we need keyguard dismissal
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
                keyguardManager.requestDismissKeyguard(this, null)
            }
        } else {
            // For older versions
            window.addFlags(
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
            )
        }
        
        // Ensure the activity appears immediately
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        
        // Apply alarm flags if this is an alarm intent
        if (intent.getBooleanExtra("LAUNCH_ALARM_SCREEN", false)) {
            setupAlarmWindowFlags()
        }
        
        handleAlarmIntent(intent)
    }

    /**
     * Returns the name of the main component registered from JavaScript. This is used to schedule
     * rendering of the component.
     */
    override fun getMainComponentName(): String = "main"

    /**
     * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
     * which allows you to enable New Architecture with a single boolean flags [fabricEnabled]
     */
    override fun createReactActivityDelegate(): ReactActivityDelegate {
        return ReactActivityDelegateWrapper(
            this,
            BuildConfig.IS_NEW_ARCHITECTURE_ENABLED,
            object : DefaultReactActivityDelegate(
                this,
                mainComponentName,
                fabricEnabled
            ){})
    }

    /**
        * Align the back button behavior with Android S
        * where moving root activities to background instead of finishing activities.
        * @see <a href="https://developer.android.com/reference/android/app/Activity#onBackPressed()">onBackPressed</a>
        */
    override fun invokeDefaultOnBackPressed() {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.R) {
            if (!moveTaskToBack(false)) {
                // For non-root activities, use the default implementation to finish them.
                super.invokeDefaultOnBackPressed()
            }
            return
        }

        // Use the default back button implementation on Android S
        // because it's doing more than [Activity.moveTaskToBack] in fact.
        super.invokeDefaultOnBackPressed()
    }

    private fun handleAlarmIntent(intent: Intent) {
        if (intent.getBooleanExtra("LAUNCH_ALARM_SCREEN", false)) {
            val alarmData = intent.getStringExtra("ALARM_DATA") ?: return
            
            // Always save alarm data to SharedPreferences as fallback
            val sharedPrefs = getAlarmTempPreferences()
            sharedPrefs.edit().putString("PENDING_ALARM_DATA", alarmData).apply()
            Log.d("AlarmDebug", "Saved alarm data to SharedPreferences: $alarmData")
            
            // Pass alarm data to React Native
            val reactInstanceManager = reactNativeHost.reactInstanceManager
            val reactContext = reactInstanceManager.currentReactContext
            
            if (reactContext != null) {
                // If React context is already initialized, send event directly
                sendAlarmEvent(reactContext, alarmData)
            } else {
                // If React context isn't ready yet, wait for it
                reactInstanceManager.addReactInstanceEventListener(object : ReactInstanceManager.ReactInstanceEventListener {
                    override fun onReactContextInitialized(reactContext: ReactContext) {
                        Log.d("AlarmDebug", "React context initialized, scheduling event dispatch")
                        // Try multiple times with increasing delays
                        scheduleEventDispatch(reactContext, alarmData, 0)
                        reactInstanceManager.removeReactInstanceEventListener(this)
                    }
                })
                
                // Start React Native
                if (!reactInstanceManager.hasStartedCreatingInitialContext()) {
                    reactInstanceManager.createReactContextInBackground()
                }
            }
        }
    }
    
    private fun scheduleEventDispatch(reactContext: ReactContext, alarmData: String, attemptCount: Int) {
        if (attemptCount > 5) {
            Log.e("AlarmDebug", "Failed to dispatch event after maximum attempts")
            return
        }
        
        // Check if the alarm has already been handled
        val sharedPrefs = getAlarmTempPreferences()
        val pendingAlarm = sharedPrefs.getString("PENDING_ALARM_DATA", null)
        
        // If pendingAlarm is null or different from our alarmData, it means either
        // 1. The alarm was already processed and cleared
        // 2. We're trying to process an outdated alarm
        // In either case, we should stop retrying
        if (pendingAlarm == null || pendingAlarm != alarmData) {
            Log.d("AlarmDebug", "Alarm already processed or outdated, stopping retries")
            return
        }
        
        val delayMs = if (attemptCount == 0) 1000 else 1000 * (1 shl attemptCount) // Exponential backoff
        Log.d("AlarmDebug", "Scheduling event dispatch attempt $attemptCount in ${delayMs}ms")
        
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                // Check again right before sending, to ensure it hasn't been processed
                val currentPendingAlarm = getAlarmTempPreferences()
                    .getString("PENDING_ALARM_DATA", null)
                
                if (currentPendingAlarm == null || currentPendingAlarm != alarmData) {
                    Log.d("AlarmDebug", "Alarm processed by another attempt, skipping")
                    return@postDelayed
                }
                
                Log.d("AlarmDebug", "Attempting to send AlarmTriggered event (attempt $attemptCount)")
                sendAlarmEvent(reactContext, alarmData)
            } catch (e: Exception) {
                Log.e("AlarmDebug", "Error sending event (attempt $attemptCount): ${e.message}", e)
                scheduleEventDispatch(reactContext, alarmData, attemptCount + 1)
            }
        }, delayMs.toLong())
    }

    private fun sendAlarmEvent(reactContext: ReactContext, alarmData: String) {
        Log.d("AlarmDebug", "sendAlarmEvent called with data: $alarmData")
        try {
            val jsModule = reactContext.getJSModule(DeviceEventManagerModule.RCTDeviceEventEmitter::class.java)
            jsModule.emit("AlarmTriggered", alarmData)
            Log.d("AlarmDebug", "Event AlarmTriggered emitted successfully")
            
            // Save locally in case we need to retry
            // val sharedPrefs = getSharedPreferences("ALARM_TEMP", MODE_PRIVATE)
            // sharedPrefs.edit().putString("PENDING_ALARM_DATA", alarmData).apply()
        } catch (e: Exception) {
            Log.e("AlarmDebug", "Error emitting event: ${e.message}", e)
        }
    }

    private fun getAlarmTempPreferences(): SharedPreferences {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            createDeviceProtectedStorageContext()
                .getSharedPreferences("ALARM_TEMP", MODE_PRIVATE)
        } else {
            getSharedPreferences("ALARM_TEMP", MODE_PRIVATE)
        }
    }
}