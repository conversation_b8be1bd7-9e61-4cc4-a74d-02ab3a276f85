{"logs": [{"outputFile": "com.cyberself.copilot.app-mergeDebugResources-19:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\9e0f8c5c01474dac9a9c9809b985e66b\\transformed\\jetified-core-splashscreen-1.0.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "85", "endOffsets": "136"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10\\transforms\\6dcf52ae5421d9e678985198d2b2979b\\transformed\\appcompat-1.6.1\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "141,211,295,379,475,577,679,773", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "206,290,374,470,572,674,768,857"}}]}]}