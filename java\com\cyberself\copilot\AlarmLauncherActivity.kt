package com.cyberself.copilot

import android.app.Activity
import android.app.KeyguardManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.PowerManager
import android.util.Log
import android.view.WindowManager

class AlarmLauncherActivity : Activity() {
    private val TAG = "AlarmLauncherActivity"
    private var wakeLock: PowerManager.WakeLock? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        Log.d(TAG, "AlarmLauncherActivity started")

        // Acquire wake lock to ensure screen stays on
        val powerManager = getSystemService(Context.POWER_SERVICE) as PowerManager
        wakeLock = powerManager.newWakeLock(
            PowerManager.SCREEN_BRIGHT_WAKE_LOCK or
            PowerManager.ACQUIRE_CAUSES_WAKEUP or
            PowerManager.ON_AFTER_RELEASE,
            "com.cyberself.copilot:AlarmLauncherWakeLock"
        ).apply {
            acquire(30000L) // 30 seconds timeout
        }

        // Set up window to show over lock screen - Enhanced for all Android versions
        setupWindowFlags()

        // Launch MainActivity with alarm data after a short delay
        val alarmData = intent.getStringExtra("ALARM_DATA")
        Log.d(TAG, "Launching MainActivity with alarm data: $alarmData")

        Handler(Looper.getMainLooper()).postDelayed({
            launchMainActivity(alarmData)
        }, 500) // Small delay to ensure screen is fully awake
    }

    private fun setupWindowFlags() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O_MR1) {
            // Android 8.1+
            setShowWhenLocked(true)
            setTurnScreenOn(true)

            // Request to dismiss keyguard
            val keyguardManager = getSystemService(Context.KEYGUARD_SERVICE) as KeyguardManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                keyguardManager.requestDismissKeyguard(this, null)
            }

            // Additional window flags for better compatibility
            window.addFlags(
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
            )
        } else {
            // Android 8.0 and below
            window.addFlags(
                WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD or
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                WindowManager.LayoutParams.FLAG_ALLOW_LOCK_WHILE_SCREEN_ON
            )
        }

        // Make window appear on top
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        }
    }

    private fun launchMainActivity(alarmData: String?) {
        try {
            val mainIntent = Intent(this, MainActivity::class.java).apply {
                addFlags(
                    Intent.FLAG_ACTIVITY_NEW_TASK or
                    Intent.FLAG_ACTIVITY_CLEAR_TOP or
                    Intent.FLAG_ACTIVITY_SINGLE_TOP or
                    Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT
                )
                putExtra("LAUNCH_ALARM_SCREEN", true)
                putExtra("ALARM_DATA", alarmData)
            }

            startActivity(mainIntent)
            Log.d(TAG, "MainActivity launched successfully")

            // Close this launcher activity after a delay
            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 1000)

        } catch (e: Exception) {
            Log.e(TAG, "Failed to launch MainActivity: ${e.message}", e)
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // Release wake lock
        wakeLock?.let {
            if (it.isHeld) {
                it.release()
            }
        }
        Log.d(TAG, "AlarmLauncherActivity destroyed")
    }
}